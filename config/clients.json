{"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/dev/pyproject/huggingface/demo1/qwen3", "/Users/<USER>/Desktop", "/Users/<USER>/Downloads"], "description": "filesystem", "lastUpdated": "2025-06-25T03:26:38.117Z"}, "time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone=Asia/Shanghai"], "description": "time", "lastUpdated": "2025-06-25T03:26:38.718Z"}, "weather": {"command": "uv", "args": ["--directory", "/Users/<USER>/dev/mcpproject/rim-mcp", "run", "rim-mcp.py"], "description": "天气服务", "lastUpdated": "2025-06-25T03:26:38.869Z"}}