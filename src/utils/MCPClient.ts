import axios from 'axios';
import { LLMService } from '../services/OpenAIService';
import { getDefaultModelId, getProviderById, getDefaultProviderId } from '../services/ModelProviders';
import type { MCPServerConfig } from '../composables/useMCPSettings';
import MCPService from '../services/MCPService';

interface Tool {
  name: string;
  description: string;
  inputSchema: {
    type: string;
    properties: Record<string, any>;
    required?: string[];
  };
}

interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: number;
  isComplete?: boolean;
  toolCalls?: Array<{
    name: string;
    params: any;
    result?: any;
    error?: string;
    success: boolean;
    timestamp: number;
  }>;
}

export class MCPClient {
  private apiKey: string = '';
  private serverUrl: string = '';
  private availableTools: Tool[] = [];
  private providerId: string = 'openai'; // 默认使用OpenAI
  private llmService: LLMService | null = null;
  private messageHistory: Message[] = [];
  private mcpServers: MCPServerConfig[] = [];
  private mcpTools: Record<string, Tool[]> = {}; // 每个MCP服务器的工具列表

  private async initializeLLMService(apiKey?: string): Promise<void> {
    try {
      // 获取提供商ID
      this.providerId = localStorage.getItem('providerId') || getDefaultProviderId();

      // 获取API密钥
      let newApiKey = apiKey || '';
      if (!newApiKey) {
        const savedApiKeys = localStorage.getItem('providerApiKeys');
        if (savedApiKeys) {
          const apiKeys = JSON.parse(savedApiKeys);
          if (apiKeys[this.providerId]) {
            newApiKey = apiKeys[this.providerId];
          }
        }
        // 回退到全局API密钥
        newApiKey = newApiKey || localStorage.getItem('apiKey') || this.apiKey;
      }

      // 如果API密钥有变化，更新它
      if (newApiKey && newApiKey !== this.apiKey) {
        this.apiKey = newApiKey;
      }

      // 获取模型ID和基础URL
      const provider = getProviderById(this.providerId);
      let baseUrl = provider?.baseUrl || '';
      let modelId = '';

      if (this.providerId === 'custom') {
        baseUrl = localStorage.getItem('customBaseUrl') || '';
        modelId = localStorage.getItem('customModelId') || '';

        // 如果没有找到自定义模型ID，尝试从自定义模型列表中获取第一个
        if (!modelId) {
          try {
            const customModels = JSON.parse(localStorage.getItem('customModels') || '[]');
            if (customModels.length > 0) {
              modelId = customModels[0].id;
            }
          } catch (e) {
            console.error('解析自定义模型列表失败', e);
          }
        }
      } else {
        modelId = localStorage.getItem('modelId') || getDefaultModelId(this.providerId);
      }

      // 创建LLM服务实例
      this.llmService = new LLMService({
        apiKey: this.apiKey,
        baseUrl,
        model: modelId,
        providerId: this.providerId
      });

      console.log(`初始化LLM服务成功 - 提供商: ${this.providerId}, 模型: ${modelId}, API密钥已设置`);
    } catch (error) {
      console.error('初始化LLM服务失败:', error);
    }
  }

  constructor() {
    // 从环境变量、localStorage和默认值获取配置
    const savedApiKey = localStorage.getItem('apiKey');
    this.apiKey = savedApiKey || import.meta.env.VITE_API_KEY || '';
    this.serverUrl = import.meta.env.VITE_MCP_SERVER_URL || 'http://localhost:3001';
    this.providerId = localStorage.getItem('providerId') || import.meta.env.VITE_MODEL_PROVIDER || 'openai';

    // 获取MCP服务器配置
    try {
      const savedMcpServers = localStorage.getItem('mcpServers');
      if (savedMcpServers) {
        this.mcpServers = JSON.parse(savedMcpServers);
      }
    } catch (error) {
      console.error('加载MCP服务器配置失败:', error);
    }

    // 检查API Key是否存在
    if (!this.apiKey) {
      console.warn('API密钥未设置，请在设置中配置API密钥');
    }

    // 初始化LLM服务
    this.initializeLLMService(this.apiKey);

    // 添加系统消息
    this.messageHistory.push({
      role: 'system',
      content: `你是一个专业的任务执行专家，擅长使用工具帮助用户完成任务。

工作原则：
1. 仔细分析用户的完整需求
2. 制定包含所有必要步骤的执行计划
3. 一次性返回所有需要的工具调用

重要：
- 当任务需要多个步骤时，必须同时调用所有需要的工具
- 按照逻辑顺序安排工具调用
- 不要分步骤执行，要一次性完成

数据传递说明：
- write_file的content参数可以使用描述性语言，系统会自动处理数据传递
- 例如："将读取的文件内容与获取的时间结合"

示例：如果用户要求"读取文件A，添加时间戳，保存为文件B"，你应该同时调用：
- read_file（读取文件A）
- get_current_time（获取时间戳）
- write_file（保存到文件B）`
    });
  }

  /**
   * 初始化MCP客户端，连接到服务端并获取可用工具
   */
  async initialize(): Promise<void> {
    console.log('初始化MCP客户端...');
    try {
      // 更新MCP服务器配置
      try {
        const savedMcpServers = localStorage.getItem('mcpServers');
        if (savedMcpServers) {
          this.mcpServers = JSON.parse(savedMcpServers);
          console.log(`已从本地存储加载 ${this.mcpServers.length} 个MCP服务器配置`);
        }
      } catch (error) {
        console.error('加载MCP服务器配置失败:', error);
      }

      // 初始化工具数据结构，但不加载具体工具（只清空和准备）
      this.mcpTools = {};
      this.availableTools = [];

      // 连接所有启用的服务器并加载工具
      await this.connectEnabledServers();

      // 去重工具列表，确保没有重复工具
      this.deduplicateTools();

      // 更新LLM服务中的工具列表，确保所有工具都可用
      if (this.llmService && this.availableTools.length > 0) {
        console.log(`最终更新LLM服务工具列表，工具数量: ${this.availableTools.length}`);
        this.llmService.updateTools(this.availableTools);
      }

      console.log('使用的模型提供商:', this.providerId);
    } catch (error) {
      console.error('连接MCP服务端失败，将使用本地可用的工具:', error);
    }
  }

  /**
   * 连接所有启用的服务器并加载工具
   */
  private async connectEnabledServers(): Promise<void> {
    // 获取所有启用的服务器
    const enabledServers = this.mcpServers.filter(server => server.enabled);
    console.log(`尝试连接 ${enabledServers.length} 个启用的服务器...`);

    // 并行连接所有服务器
    const connectionPromises = enabledServers.map(async (server) => {
      try {
        console.log(`尝试连接服务器: ${server.id}`);
        await this.connectToServer(server);

        // 获取工具列表
        const tools = await this.getMcpServerTools(server.id);

        // 触发工具更新事件
        this.dispatchToolsUpdate(server.id, tools);

        return {
          serverId: server.id,
          success: true,
          toolCount: tools.length
        };
      } catch (error) {
        console.error(`连接服务器 ${server.id} 失败:`, error);
        return {
          serverId: server.id,
          success: false,
          error
        };
      }
    });

    // 等待所有连接完成
    const results = await Promise.all(connectionPromises);
    // 计算总工具数
    const totalTools = results.reduce((sum, result) => {
      return sum + (result.success ? (result as any).toolCount : 0);
    }, 0);
    // 触发全局工具更新事件
    this.dispatchTotalToolsUpdate(totalTools);

    // 确保LLMService使用最新的工具列表
    if (this.llmService && this.availableTools.length > 0) {
      console.log(`更新LLM服务工具列表，工具数量: ${this.availableTools.length}`);
      // 这里不需要再次调用连接，只需要确保工具列表在LLM服务中更新
      this.llmService.updateTools(this.availableTools);
    } else {
      console.log(`无法更新LLM服务工具列表: ${this.llmService ? '无可用工具' : 'LLM服务未初始化'}`);
    }
  }

  /**
   * 派发工具更新事件
   * @param serverId 服务器ID
   * @param tools 工具列表
   */
  private dispatchToolsUpdate(serverId: string, tools: Tool[]): void {
    // 创建一个自定义事件
    const event = new CustomEvent('mcp-tools-update', {
      detail: {
        serverId,
        tools
      }
    });

    // 派发事件
    window.dispatchEvent(event);
    console.log(`已派发 ${serverId} 服务器工具更新事件，工具数量: ${tools.length}`);
  }

  /**
   * 派发工具总数更新事件
   * @param totalCount 工具总数
   */
  private dispatchTotalToolsUpdate(totalCount: number): void {
    // 创建一个自定义事件
    const event = new CustomEvent('mcp-total-tools-update', {
      detail: {
        totalCount
      }
    });

    // 派发事件
    window.dispatchEvent(event);
    console.log(`已派发工具总数更新事件，总工具数: ${totalCount}`);
  }

  /**
   * 获取特定MCP服务器的工具列表
   * @param serverId 服务器ID
   */
  async getMcpServerTools(serverId: string): Promise<Tool[]> {
    // 找到对应的服务器
    const server = this.mcpServers.find(s => s.id === serverId && s.enabled);
    if (!server) {
      throw new Error(`服务器 ${serverId} 不存在或未启用`);
    }

    try {
      // 根据服务器类型获取工具
      if (server.transport === 'sse') {
        // 从HTTP服务器获取工具
        const response = await axios.get(`${server.url}/tools`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`
          }
        });

        let tools = response.data?.tools || [];

        // 转换工具格式
        tools = tools.map((tool:any) => ({
          name: tool.name,
          description: tool.description,
          inputSchema: {
            type: 'object',
            properties: tool.inputSchema?.properties || {},
            required: tool.inputSchema?.required || []
          }
        }));

        // 更新工具缓存
        this.mcpTools[server.id] = tools;

        // 更新可用工具列表，保留原始工具名称
        this.availableTools = [...this.availableTools, ...tools];

        console.log(`可用工具列表更新，当前工具总数: ${this.availableTools.length}`);

        console.log(`从服务器 ${server.name} 获取了 ${tools.length} 个工具`);
        return tools;
      } else if (server.transport === 'stdio') {
        // 使用MCPService获取工具
        const tools:any = await MCPService.getTools(server.id);
        // 更新工具缓存
        this.mcpTools[server.id] = tools || [];


        // 更新可用工具列表，保留原始工具名称
        if (tools && tools.tools.length > 0) {
          this.availableTools = [...this.availableTools, ...tools.tools];
          console.log(`更新后可用工具总数: ${this.availableTools.length}`);
        } else {
          console.log(`服务器 ${server.name} 没有返回任何工具`);
        }

        return tools.tools;
      } else {
        throw new Error(`不支持的服务器传输类型: ${server.transport}`);
      }
    } catch (error) {
      console.error(`获取服务器 ${server.name} 的工具失败:`, error);
      throw error;
    }
  }


  /**
   * 获取当前使用的提供商ID
   */
  getProviderId(): string {
    return this.providerId;
  }

  /**
   * 获取当前使用的模型ID
   */
  getModel(): string {
    if (this.llmService) {
      return this.llmService.getModel();
    }

    // 根据提供商类型返回默认值
    if (this.providerId === 'custom') {
      return localStorage.getItem('customModelId') || '';
    } else {
      return localStorage.getItem('modelId') || getDefaultModelId(this.providerId);
    }
  }

  /**
   * 处理用户查询，使用LLM和可用工具
   */
  async processQuery(query: string): Promise<string> {
    // 添加用户消息到历史
    this.messageHistory.push({
      role: 'user',
      content: query
    });

    try {
      // 如果LLM服务已初始化，直接使用
      if (this.llmService) {
        // 确保工具列表是最新的
        const tools = this.availableTools && this.availableTools.length > 0 ? this.availableTools : undefined;
        console.log(`处理用户查询，使用 ${tools?.length || 0} 个工具`);

        // 直接使用LLM服务
        const response = await this.llmService.sendMessage(
          this.messageHistory,
          tools
        );

        try {
          // 检查是否为工具调用的JSON响应
          const responseData = JSON.parse(response);

          if (responseData.type === 'tool_calls' && responseData.tool_calls?.length > 0) {
            // 处理工具调用
            let finalResponse = '';
            const toolResults = new Map(); // 存储工具调用结果

            for (const call of responseData.tool_calls) {
              try {
                // 智能处理工具参数
                const processedArguments = this.processSmartToolArguments(call.name, call.arguments, toolResults);
                const toolResult = await this.callTool(call.name, processedArguments);
                finalResponse += `使用工具 ${call.name} 的结果：\n${JSON.stringify(toolResult.result, null, 2)}\n\n`;
              } catch (error) {
                finalResponse += `调用工具 ${call.name} 失败：${(error as Error).message}\n\n`;
              }
            }

            // 将工具调用结果添加到消息历史
            this.messageHistory.push({
              role: 'assistant',
              content: finalResponse
            });

            return finalResponse;
          }
        } catch (e) {
          // 不是JSON，说明是普通文本回复
        }

        // 将助手回复添加到消息历史
        this.messageHistory.push({
          role: 'assistant',
          content: response
        });

        return response;
      } else {
        // 使用服务端
        const response = await axios.post(
          `${this.serverUrl}/chat`,
          {
            query,
            tools: this.availableTools.map(tool => tool.name),
            provider: this.providerId
          },
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json'
            }
          }
        );

        const responseText = response.data?.response || '无响应';

        // 将助手回复添加到消息历史
        this.messageHistory.push({
          role: 'assistant',
          content: responseText
        });

        return responseText;
      }
    } catch (error) {
      console.error('处理查询失败:', error);
      const errorMessage = '处理查询时出错: ' + (error as Error).message;

      // 将错误消息添加到历史
      this.messageHistory.push({
        role: 'assistant',
        content: errorMessage
      });

      throw new Error(errorMessage);
    }
  }

  /**
   * 调用工具并获取结果
   * @param toolName 工具名称
   * @param args 参数对象
   * @returns 工具调用结果，包含卡片信息
   */
  async callTool(toolName: string, args: any): Promise<{result: any, success: boolean, error?: string, card?: any}> {
    try {
      // 查找工具所属的服务器
      let serverId = '';
      for (const [id, tools] of Object.entries(this.mcpTools)) {
        if (tools.some(tool => tool.name === toolName)) {
          serverId = id;
          break;
        }
      }

      if (!serverId) {
        console.warn(`找不到工具 ${toolName} 所属的服务器，尝试使用默认服务器`);
      }

      console.log(`调用工具 ${toolName}，服务器: ${serverId || '默认'}，参数:`, args);

      try {
        // 使用MCPService调用工具
        const result = await MCPService.callTool({
          name: toolName,
          arguments: args,
          clientName: serverId
        });

        console.log(`工具 ${toolName} 调用成功，结果:`, result);

        // 构建包含卡片信息的结果对象
        const response = {
          success: true,
          result,
          // 添加卡片信息
          card: {
            title: toolName,  // 工具名称作为卡片标题
            content: typeof result === 'object' ? JSON.stringify(result) : String(result),
            type: 'tool_call',
            toolName: toolName,
            serverId: serverId || 'default'
          }
        };

        // 不再触发重复的事件，由上层调用者处理
        // const event = new CustomEvent('mcp-tool-call-complete', {
        //   detail: {
        //     name: toolName,
        //     args,
        //     result: response
        //   }
        // });
        // window.dispatchEvent(event);

        return response;
      } catch (error) {
        console.error(`使用全局MCPService调用工具 ${toolName} 失败:`, error);

        // 尝试在各个服务器中查找此工具并调用
        for (const server of this.mcpServers.filter(s => s.enabled)) {
          const serverTools = this.mcpTools[server.id] || [];

          // 检查工具是否存在于此服务器
          if (Array.isArray(serverTools) && serverTools.some(tool => tool.name === toolName)) {
            console.log(`在服务器 ${server.id} 中找到工具 ${toolName}，尝试调用`);

            // 这里可以添加特定服务器的调用逻辑
            throw error; // 如果没有特定逻辑，继续抛出错误
          }
        }
        throw error;
      }
    } catch (error) {
      console.error(`调用工具 ${toolName} 失败:`, error);

      // 构建包含错误卡片信息的结果对象
      const errorResponse = {
        success: false,
        error: (error as Error).message,
        result: null,
        // 添加错误卡片信息
        card: {
          title: toolName,  // 工具名称作为卡片标题
          content: (error as Error).message,
          type: 'tool_call_error',
          toolName: toolName,
          error: true
        }
      };

      // 不再触发重复的错误事件，由上层调用者处理
      // const event = new CustomEvent('mcp-tool-call-error', {
      //   detail: {
      //     name: toolName,
      //     args,
      //     error: errorResponse
      //   }
      // });
      // window.dispatchEvent(event);

      return errorResponse;
    }
  }

  /**
   * 切换模型
   * @param modelId 模型ID
   * @param providerId 可选的提供商ID
   */
  async switchModel(modelId: string, providerId?: string): Promise<void> {
    try {
      if (providerId) {
        this.providerId = providerId;
      }

      let baseUrl = '';
      const provider = getProviderById(this.providerId);

      if (provider) {
        baseUrl = provider.baseUrl || '';
      }

      if (this.providerId === 'custom') {
        baseUrl = localStorage.getItem('customBaseUrl') || '';

        // 保存自定义模型ID
        localStorage.setItem('customModelId', modelId);
      } else {
        // 保存标准模型ID
        localStorage.setItem('modelId', modelId);
      }

      // 更新API密钥（尝试从提供商特定API密钥中获取）
      const savedApiKeys = localStorage.getItem('providerApiKeys');
      if (savedApiKeys) {
        const apiKeys = JSON.parse(savedApiKeys);
        if (apiKeys[this.providerId]) {
          this.apiKey = apiKeys[this.providerId];
        }
      }

      // 更新LLM服务
      this.llmService = new LLMService({
        apiKey: this.apiKey,
        baseUrl: baseUrl,
        model: modelId,
        providerId: this.providerId
      });

      console.log(`已切换到模型: ${modelId}, 提供商: ${this.providerId}`);
    } catch (error) {
      console.error('切换模型失败:', error);
      throw new Error('切换模型失败');
    }
  }

  /**
   * 更新MCP服务器配置
   * @param servers MCP服务器配置列表
   */
  updateMcpServers(servers: MCPServerConfig[]): void {
    this.mcpServers = servers;
    this.refreshAvailableTools();
  }

  /**
   * 清除消息历史
   */
  clearHistory(): void {
    this.messageHistory = [];
    // 重新添加系统消息
    this.messageHistory.push({
      role: 'system',
      content: `你是一个专业的任务执行专家，基于MCP协议调用各种工具帮助用户完成任务。

作为专家，你的工作方式是：
1. 仔细分析用户的完整需求
2. 制定包含所有必要步骤的执行计划
3. 一次性返回完整的工具调用序列，不分步骤执行

重要原则：
- 当任务需要多个步骤时，必须在一个响应中返回所有工具调用
- 按照逻辑顺序安排工具调用
- 确保每个工具调用都有正确的参数
- 不要逐步执行，要一次性规划完整流程

示例：如果用户要求"读取文件A，添加时间戳，保存为文件B"，你应该返回：
1. read_file (读取文件A)
2. get_current_time (获取时间戳)
3. write_file (保存到文件B，内容包含原文+时间戳)`
    });
  }

  /**
   * 验证和清理消息历史
   * @param messages 消息列表
   * @returns 清理后的消息列表
   */
  private validateAndCleanMessages(
    messages: Array<Message>
  ): Array<Message> {
    console.log('开始验证和清理消息历史，原始数量:', messages.length);

    if (!messages || !Array.isArray(messages)) {
      console.warn('消息列表无效，返回空数组');
      return [];
    }

    const cleanedMessages: Message[] = [];

    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];

      // 验证消息结构
      if (!msg || typeof msg !== 'object') {
        console.warn(`跳过无效消息 [${i}]: 不是对象`);
        continue;
      }

      if (!msg.role || typeof msg.role !== 'string') {
        console.warn(`跳过无效消息 [${i}]: 缺少或无效的role`);
        continue;
      }

      if (msg.content === undefined || msg.content === null || typeof msg.content !== 'string') {
        console.warn(`跳过无效消息 [${i}]: 缺少或无效的content`);
        continue;
      }

      // 清理内容
      const cleanedContent = msg.content.trim();
      if (cleanedContent.length === 0) {
        console.warn(`跳过空内容消息 [${i}]: role=${msg.role}`);
        continue;
      }

      // 验证角色
      if (!['user', 'assistant', 'system'].includes(msg.role)) {
        console.warn(`跳过无效角色消息 [${i}]: role=${msg.role}`);
        continue;
      }

      // 创建新消息对象
      const newMessage: Message = {
        role: msg.role as 'user' | 'assistant' | 'system',
        content: cleanedContent
      };
      
      // 保留其他重要字段
      if (msg.timestamp) newMessage.timestamp = msg.timestamp;
      if (msg.isComplete !== undefined) newMessage.isComplete = msg.isComplete;
      
      // 保留工具调用信息
      if (msg.toolCalls && Array.isArray(msg.toolCalls) && msg.toolCalls.length > 0) {
        console.log(`保留消息 [${i}] 的工具调用信息，工具数量: ${msg.toolCalls.length}`);
        newMessage.toolCalls = [...msg.toolCalls];
      }
      
      cleanedMessages.push(newMessage);
    }

    console.log(`消息验证完成: 原始 ${messages.length} 条 -> 有效 ${cleanedMessages.length} 条`);

    return cleanedMessages;
  }

  /**
   * 添加消息到历史记录
   */
  addMessageToHistory(message: Message): void {
    // 验证消息格式
    if (!message || typeof message !== 'object') {
      console.warn('无效消息，无法添加到历史记录');
      return;
    }

    if (!message.role || typeof message.role !== 'string') {
      console.warn('消息缺少或无效的role，无法添加到历史记录');
      return;
    }

    if (message.content === undefined || message.content === null || typeof message.content !== 'string') {
      console.warn('消息缺少或无效的content，无法添加到历史记录');
      return;
    }

    const cleanedContent = message.content.trim();
    if (cleanedContent.length === 0) {
      console.warn('消息内容为空，无法添加到历史记录');
      return;
    }

    if (!['user', 'assistant', 'system'].includes(message.role)) {
      console.warn(`无效的消息角色: ${message.role}，无法添加到历史记录`);
      return;
    }

    // 添加清理后的消息，保留工具调用信息
    const newMessage: Message = {
      role: message.role as 'user' | 'assistant' | 'system',
      content: cleanedContent
    };
    
    // 保留其他重要字段
    if (message.timestamp) newMessage.timestamp = message.timestamp;
    if (message.isComplete !== undefined) newMessage.isComplete = message.isComplete;
    
    // 保留工具调用信息
    if (message.toolCalls && Array.isArray(message.toolCalls) && message.toolCalls.length > 0) {
      console.log(`保存工具调用信息到历史记录，工具数量: ${message.toolCalls.length}`);
      newMessage.toolCalls = [...message.toolCalls];
    }

    this.messageHistory.push(newMessage);

    console.log(`添加消息到历史记录: ${message.role}, 内容长度: ${cleanedContent.length}, 包含工具调用: ${!!newMessage.toolCalls}`);
  }

  /**
   * 获取可用工具列表
   */
  getAvailableTools(): Tool[] {
    return this.availableTools;
  }

  /**
   * 处理用户查询，使用LLM和可用工具，支持流式响应
   * @param query 用户查询
   * @param onChunk 处理响应块的回调函数
   * @param onToolCall 处理工具调用的回调函数
   */
  async processStreamQuery(
    query: string,
    onChunk: (chunk: string) => void,
    onToolCall?: (toolCall: { name: string, params: any, result?: any, error?: string, success: boolean }) => void
  ): Promise<string> {
    // 验证和清理用户输入
    const cleanedQuery = query?.trim();
    if (!cleanedQuery || cleanedQuery.length === 0) {
      throw new Error('用户输入不能为空');
    }

    // 添加用户消息到历史
    this.messageHistory.push({
      role: 'user',
      content: cleanedQuery
    });

    // 验证和清理消息历史
    this.messageHistory = this.validateAndCleanMessages(this.messageHistory);

    // 解码HTML实体函数
    const decodeHTMLEntities = (text: string): string => {
      const textArea = document.createElement('textarea');
      textArea.innerHTML = text;
      return textArea.value;
    };

    // 封装onChunk回调，确保解码HTML实体
    const safeOnChunk = (chunk: string) => {
      const decodedChunk = decodeHTMLEntities(chunk);
      onChunk(decodedChunk);
    };

    try {
      // 如果LLM服务已初始化，直接使用
      if (this.llmService) {
        // 确保工具列表是最新的
        const tools = this.availableTools && this.availableTools.length > 0 ? this.availableTools : undefined;
        console.log(`处理用户流式查询，使用 ${tools?.length || 0} 个工具`);

        // 验证消息历史在发送前
        const validatedHistory = this.validateAndCleanMessages(this.messageHistory);
        if (validatedHistory.length === 0) {
          throw new Error('没有有效的消息历史');
        }

        // 使用流式API
        const response = await this.llmService.sendStreamMessage(
          validatedHistory,
          safeOnChunk,
          tools,
          onToolCall // 传递工具调用回调
        );

        let isToolCall = false;

        try {
          // 检查是否为工具调用的JSON响应
          const responseData = JSON.parse(response);

          // 检查是否为Claude原生的tool_use格式
          let toolCalls = [];
          if (responseData.content && Array.isArray(responseData.content)) {
            for (const content of responseData.content) {
              if (content.type === 'tool_use') {
                toolCalls.push({
                  name: content.name,
                  arguments: content.input || {}
                });
              }
            }
          }

          // 或者检查是否为我们的工具调用格式
          if (responseData.type === 'tool_calls' && responseData.tool_calls?.length > 0) {
            toolCalls = responseData.tool_calls;
          }

          if (toolCalls.length > 0) {
            isToolCall = true;
            // 处理工具调用
            let finalResponse = '';
            const toolResults = new Map(); // 存储工具调用结果

            for (const call of toolCalls) {
              // 在外部作用域定义这些变量
              let toolResult: any = null;
              let success = true;
              let errorMessage = '';

              try {
                console.log(`开始调用工具: ${call.name}`);

                try {
                  // 智能处理工具参数
                  const processedArguments = this.processSmartToolArguments(call.name, call.arguments, toolResults);
                  toolResult = await this.callTool(call.name, processedArguments);
                  success = toolResult?.success !== false;

                  // 存储工具调用结果供后续使用
                  if (success && toolResult?.result) {
                    let extractedResult = toolResult.result;
                    if (toolResult.result.content && Array.isArray(toolResult.result.content)) {
                      extractedResult = toolResult.result.content
                        .filter((item: {type: string}) => item.type === 'text')
                        .map((item: {text: string}) => item.text)
                        .join('\n');
                    }
                    toolResults.set(call.name, extractedResult);
                    console.log(`存储工具 ${call.name} 的结果:`, extractedResult.substring(0, 100) + '...');
                  }
                } catch (error) {
                  success = false;
                  errorMessage = (error as Error).message;
                  console.error(`调用工具 ${call.name} 失败:`, error);
                }

                // 如果提供了回调，只调用一次
                if (onToolCall) {
                  // 提取工具调用的实际结果文本
                  let extractedResult = undefined;
                  if (success && toolResult?.result) {
                    if (toolResult.result.content && Array.isArray(toolResult.result.content)) {
                      // 提取 content 数组中的文本
                      extractedResult = toolResult.result.content
                        .filter((item: {type: string}) => item.type === 'text')
                        .map((item: {text: string}) => item.text)
                        .join('\n');
                    } else {
                      // 如果不是预期的格式，使用原始结果
                      extractedResult = toolResult.result;
                    }
                  }

                  onToolCall({
                    name: call.name,
                    params: call.arguments,
                    result: extractedResult,
                    error: success ? undefined : errorMessage,
                    success
                  });
                }

                // 构建工具调用的JSON格式，用于前端卡片显示
                // 使用相同的结果提取逻辑
                let displayResult = undefined;
                if (success && toolResult?.result) {
                  if (toolResult.result.content && Array.isArray(toolResult.result.content)) {
                    displayResult = toolResult.result.content
                      .filter((item: {type: string}) => item.type === 'text')
                      .map((item: {text: string}) => item.text)
                      .join('\n');
                  } else {
                    displayResult = toolResult.result;
                  }
                }

                const toolCallJson = {
                  type: 'tool_calls',
                  tool_calls: [{
                    name: call.name,
                    arguments: call.arguments,
                    result: displayResult,
                    error: success ? undefined : errorMessage,
                    success: success
                  }]
                };

                // 添加工具调用JSON到响应中，这样前端可以解析并显示为卡片
                finalResponse += JSON.stringify(toolCallJson) + '\n\n';
              } catch (error) {
                // 明确设置success为false
                success = false;
                errorMessage = (error as Error).message;
                const errorMsg = `调用工具 ${call.name} 失败：${errorMessage}\n\n`;
                finalResponse += errorMsg;
                safeOnChunk(errorMsg);
              }
            }

            // 将工具调用结果添加到消息历史
            this.messageHistory.push({
              role: 'assistant',
              content: finalResponse
            });

            // 发送工具调用结果到前端显示（只发送一次）
            safeOnChunk(finalResponse);

            // 构建工具调用结果的总结，用于AI理解
            let toolResultSummary = '工具调用结果：\n';
            for (const call of toolCalls) {
              const callResult = call.result || call.error;
              // 使用call对象中的success属性，而不是外部的success变量
              toolResultSummary += `- ${call.name}: ${call.success ? '成功' : '失败'}\n`;
              if (callResult) {
                toolResultSummary += `  结果: ${typeof callResult === 'object' ? JSON.stringify(callResult, null, 2) : callResult}\n`;
              }
            }

            // 将工具调用结果发送回AI，并询问问题是否已解决
            const followUpQuery = `${toolResultSummary}\n\n请根据以上工具调用结果，回答我的问题：${cleanedQuery}。如果问题已解决，请说明；如果没有解决，请进一步解释或尝试其他方法。`;

            this.messageHistory.push({
              role: 'user',
              content: followUpQuery
            });

            // 验证消息历史在发送前
            const validatedFollowUpHistory = this.validateAndCleanMessages(this.messageHistory);

            // 重新获取AI回复
            const followUpResponse = await this.llmService.sendStreamMessage(
              validatedFollowUpHistory,
              safeOnChunk,
              tools
            );

            // 将AI的后续回复添加到历史
            this.messageHistory.push({
              role: 'assistant',
              content: followUpResponse
            });

            return finalResponse + followUpResponse;
          }
        } catch (e) {
          // 不是JSON，说明是普通文本回复
          console.log('不是工具调用的JSON响应，是普通文本回复');
        }

        // 将助手回复添加到消息历史
        this.messageHistory.push({
          role: 'assistant',
          content: response
        });

        // 如果有工具调用但解析失败，或者没有工具调用，需要询问是否解决
        if (isToolCall) {
          // 这种情况在上面已处理
          return response;
        } else {
          // 无工具调用情况，直接返回原始响应
          return response;
        }
      } else {
        // 暂不支持服务端的流式响应，使用普通响应代替
        const responseText = await this.processQuery(query);
        safeOnChunk(responseText);
        return responseText;
      }
    } catch (error) {
      console.error('处理查询失败:', error);
      const errorMessage = '处理查询时出错: ' + (error as Error).message;

      // 将错误消息添加到历史
      this.messageHistory.push({
        role: 'assistant',
        content: errorMessage
      });

      safeOnChunk(errorMessage);
      return errorMessage;
    }
  }

  /**
   * 设置API密钥
   * @param apiKey 新的API密钥
   */
  setApiKey(apiKey: string): void {
    if (this.apiKey !== apiKey) {
      console.log('更新API密钥');
      this.apiKey = apiKey;

      // 如果LLM服务存在，更新其API密钥
      if (this.llmService) {
        this.llmService.setApiKey(apiKey);
      }
    }
  }

  /**
   * 连接到MCP服务器
   * @param serverConfig 服务器配置
   */
  async connectToServer(serverConfig: MCPServerConfig): Promise<void> {
    if (serverConfig.transport === 'stdio') {
      try {
        // 使用MCPService连接到服务器
        await MCPService.connectClient(serverConfig.id);
        console.log(`已连接到服务器 ${serverConfig.id}`);
      } catch (error) {
        console.error(`连接到服务器 ${serverConfig.id} 失败:`, error);
        throw error;
      }
    } else if (serverConfig.transport === 'sse') {
      // SSE类型服务器的连接逻辑保持不变
      try {
        console.log(`已连接到SSE服务器 ${serverConfig.id}`);
      } catch (error) {
        console.error(`连接到SSE服务器 ${serverConfig.id} 失败:`, error);
        throw error;
      }
    }
  }

  /**
   * 断开与MCP服务器的连接
   * @param serverId 服务器ID
   */
  async disconnectFromServer(serverId: string): Promise<void> {
    const server = this.mcpServers.find(s => s.id === serverId);
    if (!server) {
      throw new Error(`服务器 ${serverId} 不存在`);
    }

    // 创建匹配该服务器前缀的正则表达式
    const prefixRegex = new RegExp(`^${serverId}_`);

    if (server.transport === 'stdio') {
      try {
        // 使用MCPService断开服务器连接
        await MCPService.disconnectClient(serverId);
        console.log(`已断开与服务器 ${serverId} 的连接`);

        // 从可用工具列表中移除此服务器的工具 - 使用正则匹配前缀
        this.availableTools = this.availableTools.filter(tool => !prefixRegex.test(tool.name));

        // 清空缓存的工具列表
        this.mcpTools[serverId] = [];
      } catch (error) {
        console.error(`断开与服务器 ${serverId} 的连接失败:`, error);
        throw error;
      }
    } else if (server.transport === 'sse') {
      // SSE类型服务器无需特殊断开逻辑，只需清理工具
      this.availableTools = this.availableTools.filter(tool => !prefixRegex.test(tool.name));
      this.mcpTools[serverId] = [];
      console.log(`已断开与SSE服务器 ${serverId} 的连接`);
    }
  }

  /**
   * 刷新可用工具列表
   */
  async refreshAvailableTools(): Promise<void> {
    // 清空工具列表
    this.availableTools = [];
    this.mcpTools = {};

    // 重新连接服务器并加载工具
    await this.connectEnabledServers();

    // 尝试加载全局工具
    try {
      const globalTools = await MCPService.getTools();
      if (globalTools && globalTools.length > 0) {
        // 添加全局工具
        this.availableTools = [...this.availableTools, ...globalTools];
      }
    } catch (error) {
      console.error('刷新全局工具失败:', error);
    }

    // 执行最终去重
    this.deduplicateTools();

    console.log('已刷新所有可用工具');
  }


  /**
   * 智能处理工具调用参数，特别是write_file的content参数
   * @param toolName 工具名称
   * @param arguments 原始参数
   * @param toolResults 之前工具调用的结果Map
   * @returns 处理后的参数
   */
  private processSmartToolArguments(toolName: string, args: any, toolResults: Map<string, any>): any {
    if (!args || typeof args !== 'object') {
      return args;
    }

    const processedArgs = { ...args };

    // 特殊处理write_file的content参数
    if (toolName === 'write_file' && processedArgs.content && typeof processedArgs.content === 'string') {
      const content = processedArgs.content;

      // 检查是否是描述性内容（包含"将读取的文件内容"等描述）
      if (content.includes('将读取的文件内容') || content.includes('读取的文件') || content.includes('获取的时间')) {
        console.log('🔄 检测到描述性content，开始智能处理...');

        let processedContent = '';
        const readFileResult = toolResults.get('read_file');
        const timeResult = toolResults.get('get_current_time');

        if (content.includes('将读取的文件内容与') && content.includes('时间')) {
          // 处理"将读取的文件内容与时间结合"的情况
          if (readFileResult && timeResult) {
            if (content.includes('格式为：')) {
              // 提取格式说明
              const formatMatch = content.match(/格式为：(.+)$/);
              if (formatMatch) {
                let format = formatMatch[1];
                format = format.replace(/原文内容/g, readFileResult);
                format = format.replace(/\[时间\]/g, timeResult);
                format = format.replace(/\n/g, '\n');
                processedContent = format;
              } else {
                processedContent = `${readFileResult}\n\n--- 添加于 ${timeResult} ---`;
              }
            } else {
              processedContent = `${readFileResult}\n\n--- 添加于 ${timeResult} ---`;
            }
          } else {
            console.log('⚠️ 缺少必要的工具结果，使用原始content');
            processedContent = content;
          }
        } else if (content.includes('读取的文件内容')) {
          // 简单的文件内容替换
          if (readFileResult) {
            processedContent = content.replace(/读取的文件内容/g, readFileResult);
          } else {
            processedContent = content;
          }
        } else {
          processedContent = content;
        }

        console.log('✅ 智能处理完成，生成实际内容');
        processedArgs.content = processedContent;
      }
    }

    return processedArgs;
  }

  /**
   * 去重并规范化工具列表
   * 这个方法可以在有需要时主动调用，清理掉重复的工具
   */
  public deduplicateTools(): void {
    if (!this.availableTools.length) return;

    console.log(`开始去重工具，当前工具数量: ${this.availableTools.length}`);

    // 使用Map进行去重，以工具名称为键
    const toolsMap = new Map<string, Tool>();

    for (const tool of this.availableTools) {
      // 以工具原始名称为键进行去重
      if (!toolsMap.has(tool.name)) {
        toolsMap.set(tool.name, tool);
      }
    }
    // 重建工具列表
    this.availableTools = Array.from(toolsMap.values());

    console.log(`工具去重完成，当前工具数量: ${this.availableTools.length}`);
  }
}