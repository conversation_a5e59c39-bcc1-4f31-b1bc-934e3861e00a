/* 消息组样式 */
.message-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 32px;
  width: 100%;
  animation: message-group-appear 0.5s ease-out;
}

/* 聊天消息区域 */
.chat-messages {
  position: relative;
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 28px;
  padding-bottom: 15px;
  overflow-y: auto;
  width: calc(100% - 30px);
  max-width: 768px;
  margin: 0 auto;
}

.message {
  display: flex;
  gap: 0;
  width: 100%;
  animation: message-appear 0.4s ease-out;
  transform-origin: bottom;
  margin-bottom: 5px;
}

.message.user {
  justify-content: flex-end;
  max-width: 95%;
  margin-left: auto;
  margin-right: 0;
}

.message.assistant {
  justify-content: flex-start;
  max-width: 95%;
  margin-right: auto;
  margin-left: 0;
}

.assistant .message-avatar {
  background-color: #e7eef7;
  color: #1064a3;
}

/* 消息内容容器 - Semi Design风格 */
.message-content {
  padding: 10px 20px; /* 适当的内边距 */
  border-radius: 20px; /* 更圆润的边角 */
  position: relative;
  box-shadow: none; /* 移除阴影，使用清晰的边界 */
  max-width: 85%;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

/* AI消息样式 - Semi Design风格 */
.assistant .message-content {
  background: #f8f9fa; /* 浅灰色背景 */
  border: none;
  border-radius: 20px; /* 圆润的边角 */
  margin-right: auto;
  box-shadow: none; /* 移除阴影 */
  position: relative;
  padding: 10px 20px; /* 内边距 */
  max-width: 80%; /* 消息框宽度 */
  width: max-content; /* 根据内容自适应宽度 */
  min-width: 120px; /* 最小宽度 */
  color: #333333; /* 文字颜色 */
  font-weight: 400; /* 字重 */
}

.message.typing .message-content {
  display: flex;
  align-items: center;
  min-width: 60px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: #aaa;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

/* 生成中指示器 */
.generating-indicator {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  margin-bottom: 4px;
}

.generating-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3498db;
  opacity: 0.6;
  animation: pulse 1s infinite ease-in-out;
}

.generating-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.generating-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.generating-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

/* 消息时间显示 - 外置设计 */
.message-time {
  font-size: 0.6rem; /* 稍大一点以保证外置时的可读性 */
  color: rgba(44, 62, 80, 0.6);
  margin-top: 0;
  text-align: left;
  letter-spacing: 0.2px;
  font-weight: 300;
  opacity: 0.8; /* 增加透明度以保证可读性 */
  background: none; /* 移除背景，因为在外面 */
  padding: 0; /* 移除内边距 */
  border-radius: 0; /* 移除圆角 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* 使用系统字体 */
  line-height: 1; /* 紧凑的行高 */
  height: 14px; /* 固定高度 */
  display: flex;
  align-items: center;
}

.user .message-time {
  color: rgba(108, 117, 125, 0.8) !important; /* 使用灰色色调与消息框保持一致 */
  text-align: right; /* 右对齐 */
  background: none !important; /* 移除背景 */
  font-size: 0.6rem; /* 与通用样式保持一致 */
  height: 14px !important;
  font-weight: 400 !important;
}

.user .copy-message-button {
  color: rgba(108, 117, 125, 0.8) !important; /* 使用灰色色调 */
  background: rgba(108, 117, 125, 0.1) !important; /* 淡灰色背景 */
  transform: scale(0.85); /* 与通用样式保持一致 */
  width: 18px !important;
  height: 14px !important;
  margin-left: 4px !important;
}

.user .copy-message-button:hover {
  /* 移除所有悬停变色效果 */
}

.user .copy-message-button.copied {
  color: rgba(108, 117, 125, 1) !important;
  opacity: 1;
  background: rgba(108, 117, 125, 0.25) !important;
  transform: scale(0.9) !important;
}

.assistant .message-time {
  text-align: right;
}

/* 消息悬停效果 */
.message-content {
  /* 移除过渡动画 */
}

.assistant .message-content:hover {
  /* 移除所有悬停变色效果 */
}



.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  /* 调整顶部内边距以匹配更小的头部组件 */
  padding-top: 3.5rem;
}

.chat-content-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 28px;
  padding-bottom: 15px;
  flex: 1;
  overflow-y: auto;
  /* 已经通过全局样式隐藏了滚动条 */
}

/* 预览图面板样式 */
.preview-panel {
  width: 280px;
  min-width: 280px;
  border-left: 1px solid #eaeaea;
  background-color: #fafafa;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  /* 已经通过全局样式隐藏了滚动条 */
}

.preview-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #f5f5f5;
  position: sticky;
  top: 0;
  z-index: 1;
}

.preview-panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.close-preview-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.close-preview-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.preview-images {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-image-wrapper {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  position: relative;
}

.preview-image {
  width: 100%;
  height: auto;
  max-height: 250px;
  object-fit: contain;
  display: block;
}

.image-caption {
  padding: 8px 12px;
  font-size: 0.85rem;
  color: #666;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.preview-image-wrapper:hover .image-actions {
  opacity: 1;
}

.open-image-btn,
.download-image-btn {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  color: #555;
  transition: all 0.2s ease;
}

.open-image-btn:hover,
.download-image-btn:hover {
  background-color: white;
  color: #1064a3;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* 平滑滚动效果 */
.chat-messages {
  scroll-behavior: smooth;
  scroll-padding: 20px;
  overscroll-behavior: contain;
}

/* 工具调用容器样式 */
.tool-calls-container {
  margin-top: 12px;
  margin-bottom: 8px;
  border-top: 1px dashed #e0e0e0;
  padding-top: 8px;
}

/* 消息文本样式增强 - 超紧凑细长风格 */
.message-text {
  line-height: 1.2; /* 更紧凑的行高 */
  word-break: break-word;
  font-size: 13px; /* 与消息容器保持一致 */
  margin-top: 0; /* 移除上边距 */
  margin-bottom: 0; /* 移除下边距 */
  min-height: 12px; /* 进一步减小最小高度 */
  letter-spacing: 0.1px; /* 减小字间距 */
  font-weight: 400;
  color: inherit;
  padding-bottom: 0; /* 确保没有额外的底部内边距 */
}

/* 消息组样式优化 */
.message-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

/* 消息内容容器改进 */
.message .message-content {
  border-radius: 12px;
  padding: 12px 16px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  max-width: 90%;
  width: fit-content;
}

/* 用户消息 - 极简细长风格设计（外置时间和按钮） */
.user .message-content {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); /* 淡灰色渐变 */
  color: #212529; /* 黑色文字 */
  border: 1px solid rgba(206, 212, 218, 0.5); /* 淡灰色边框 */
  margin-left: auto;
  min-height: 20px; /* 进一步减小最小高度 */
  height: fit-content; /* 根据内容自适应高度 */
  padding: 6px 12px; /* 移除底部内边距，只保留基本内边距 */
  border-radius: 16px; /* 稍小的圆角以配合紧凑设计 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 中性的阴影 */
  max-width: 70%; /* 减小最大宽度 */
  width: max-content; /* 根据内容自适应宽度 */
  min-width: 60px; /* 进一步减小最小宽度 */
  font-weight: 400; /* 正常字重 */
  letter-spacing: 0.1px; /* 减小字间距 */
  font-size: 13px; /* 更小的字体 */
  line-height: 1.2; /* 更紧凑的行高 */
  position: relative;
  overflow: hidden;
  /* 移除过渡动画 */
}

/* 用户消息光效动画 - 已移除 */
/* 移除所有光效动画 */

/* 确保用户消息中的所有文本都是黑色 */
.user .message-content,
.user .message-content *,
.user .message-text,
.user .message-text * {
  color: #212529 !important;
}

/* 用户消息中的链接样式 */
.user .message-content a {
  color: #495057 !important; /* 深灰色链接 */
  text-decoration: underline;
}

.user .message-content a:hover {
  color: #343a40 !important; /* 悬停时更深的灰色 */
}

/* 通用消息样式优化 - 极简细长风格设计（外置时间和按钮） */
.message-content {
  border-radius: 16px; /* 稍小的圆角 */
  padding: 6px 12px; /* 统一的内边距，移除底部额外空间 */
  line-height: 1.2; /* 更紧凑的行高 */
  /* 移除过渡动画 */
  overflow: hidden;
  max-width: 70%; /* 减小最大宽度 */
  min-width: 60px; /* 进一步减小最小宽度 */
  min-height: 20px; /* 进一步减小最小高度 */
  font-size: 13px; /* 更小的字体 */
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
}

/* 用户消息悬停效果 - 移除变色效果 */
.user .message-content:hover {
  /* 移除所有悬停变色效果 */
}

/* AI消息样式 - 极简细长风格设计（外置时间和按钮） */
.assistant .message-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); /* 更亮的灰色渐变背景 */
  color: #212529; /* 与用户消息保持一致的黑色文字 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 与用户消息保持一致的阴影 */
  border: 1px solid rgba(206, 212, 218, 0.5); /* 与用户消息保持一致的边框 */
  border-radius: 16px; /* 与用户消息保持一致的圆角 */
  padding: 6px 12px; /* 移除底部内边距，只保留基本内边距 */
  font-size: 13px; /* 与用户消息保持一致的字体大小 */
  line-height: 1.2; /* 与用户消息保持一致的行高 */
  font-weight: 400;
  max-width: 70%; /* 与用户消息保持一致 */
  min-height: 20px; /* 与用户消息保持一致 */
  position: relative;
  /* 移除过渡动画 */
}

/* 改进代码块样式 */
.assistant .message-content :deep(pre) {
  background-color: #f8f9fa;
  border-radius: 6px;
  overflow-x: auto;
  position: relative;
  border: 1px solid #eaeaea;
}

/* 代码块语言标识 */
.assistant .message-content :deep(pre):before {
  content: attr(data-language);
  position: absolute;
  top: 0;
  right: 10px;
  font-size: 0.75rem;
  color: #888;
  background: #f8f9fa;
  padding: 0 8px;
  border-radius: 0 0 4px 4px;
  border: 1px solid #eaeaea;
  border-top: none;
}

/* 代码块复制按钮 */
.assistant .message-content :deep(.code-block-wrapper) {
  position: relative;
}

.assistant .message-content :deep(.code-copy-button) {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #eaeaea;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.assistant .message-content :deep(.code-block-wrapper:hover .code-copy-button) {
  opacity: 1;
}

/* 消息操作按钮样式 - 外置设计 */
.message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2px; /* 在消息框下方添加小间距 */
  position: relative; /* 改为相对定位 */
  padding: 0 4px; /* 水平内边距 */
  height: 14px; /* 固定高度 */
  width: 100%; /* 占满宽度 */
}

/* 用户消息的操作区域右对齐 */
.user .message-actions {
  justify-content: flex-end;
  padding-right: 0;
}

/* AI消息的操作区域左对齐 */
.assistant .message-actions {
  justify-content: flex-start;
  padding-left: 0;
}

.message-buttons {
  display: flex;
  gap: 4px;
}

.copy-message-button, .regenerate-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  opacity: 0.5;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-message-button {
  background: rgba(0, 0, 0, 0.05); /* 淡灰色背景 */
  border: none;
  cursor: pointer;
  opacity: 0.7; /* 增加透明度以保证可见性 */
  padding: 2px 4px; /* 适中的内边距 */
  border-radius: 4px; /* 小圆角 */
  /* 移除过渡动画 */
  margin-left: 4px; /* 与时间的间距 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transform: scale(0.85); /* 稍大一点以适应外置布局 */
  width: 18px; /* 适中的宽度 */
  height: 14px; /* 适中的高度 */
}

.copy-message-button:hover, .regenerate-button:hover {
  /* 移除所有悬停变色效果 */
}

.copy-message-button.copied {
  color: #10a37f;
  opacity: 1;
}

.regenerate-button {
  color: #3498db;
}

.regenerate-button:hover {
  /* 移除所有悬停变色效果 */
}

.user .copy-message-button {
  color: rgba(255, 255, 255, 0.9);
}

.user .copy-message-button:hover {
  /* 移除所有悬停变色效果 */
}

.user .copy-message-button.copied {
  color: #ffffff;
  opacity: 1;
}

.assistant .copy-message-button,
.assistant .regenerate-button {
  color: rgba(0, 0, 0, 0.6);
}

/* 时间戳样式优化 */
.message-time {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.4);
  text-align: left;
}

.user .message-time {
  color: rgba(255, 255, 255, 0.7);
}

/* 复制成功通知样式 */
:deep(.copy-success-notification) {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  background-color: rgba(16, 163, 127, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

:deep(.copy-success-notification.show) {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* 改进的加载指示器 */
.generating-indicator {
  display: flex;
  gap: 4px;
  margin-top: 10px;
  margin-bottom: 5px;
  align-items: center;
  justify-content: flex-start;
}

.generating-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3498db;
  opacity: 0.7;
  animation: bounce 1.4s infinite ease-in-out both;
}

.generating-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.generating-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1.0);
  }
}

/* 消息进入动画 */
.message {
  animation: message-appear 0.3s ease-out;
  transform-origin: bottom;
}

@keyframes message-appear {
  0% {
    opacity: 0;
    transform: translateY(15px) scale(0.96);
  }
  70% {
    opacity: 1;
    transform: translateY(-2px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .preview-panel {
    width: 220px;
    min-width: 220px;
  }
  .message .message-content {
    max-width: 95%;
  }
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  /* 删除自定义滚动条样式，使用全局隐藏滚动条的样式 */
}

/* 删除自定义滚动条样式
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}
*/

/* 保持工具调用清晰分离 */
.tool-calls-inline {
  margin: 0 0 16px 0;
  padding: 12px;
  border-radius: 8px;
  background-color: #f3f8ff;
  border: 1px solid #d0e1fd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 工具调用状态提示容器样式优化 */
.tool-call-status-hint {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  padding: 6px 10px;
  margin-top: 10px;
  color: #4a6586;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-left: 2px solid #4a6586;
}

.tool-call-status-hint.processing {
  color: #1e88e5;
  background-color: rgba(227, 242, 253, 0.7);
  border-left-color: #1e88e5;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.status-icon.completed {
  color: #4caf50;
}

.status-text {
  font-weight: 500;
}

/* 旋转动画 */
.rotating {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

/* 移动端响应式优化 - 极简绂长风格（外置时间和按钮） */
@media (max-width: 768px) {
  .user .message-content,
  .assistant .message-content {
    max-width: 80%; /* 保持细长效果 */
    padding: 5px 10px; /* 统一的内边距 */
    font-size: 12px;
    border-radius: 14px; /* 更小的圆角 */
    line-height: 1.15; /* 更紧凑的行高 */
    min-height: 18px; /* 进一步减小最小高度 */
  }

  .user .message-content {
    box-shadow: 0 1px 3px rgba(24, 144, 255, 0.12);
  }

  .assistant .message-content {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  }

  .message-text {
    font-size: 12px;
    line-height: 1.2;
  }

  .message-time {
    font-size: 0.55rem;
    height: 12px;
  }

  .copy-message-button {
    transform: scale(0.8);
    width: 16px;
    height: 12px;
  }

  .message-actions {
    margin-top: 1px;
    height: 12px;
  }
}

/* 小屏幕设备优化 - 极简细长风格（外置时间和按钮） */
@media (max-width: 480px) {
  .user .message-content,
  .assistant .message-content {
    max-width: 85%; /* 在小屏幕上稍微放宽 */
    padding: 4px 8px; /* 更紧凑的内边距 */
    font-size: 11px;
    border-radius: 12px;
    line-height: 1.1;
    min-height: 16px;
  }

  .message-text {
    font-size: 11px;
    line-height: 1.15;
  }

  .message-time {
    font-size: 0.5rem;
    height: 10px;
  }

  .copy-message-button {
    transform: scale(0.75);
    width: 14px;
    height: 10px;
  }

  .message-actions {
    margin-top: 1px;
    height: 10px;
  }
}